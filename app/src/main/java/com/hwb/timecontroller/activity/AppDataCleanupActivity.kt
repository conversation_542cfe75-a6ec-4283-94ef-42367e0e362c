package com.hwb.timecontroller.activity

import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hwb.timecontroller.R
import com.hwb.timecontroller.adapter.AppCleanupProgressAdapter
import com.hwb.timecontroller.business.AdminManager
import com.hwb.timecontroller.viewModel.AppDataCleanupViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.elvishew.xlog.XLog

/**
 * 应用数据清理Activity
 * 全屏Loading样式的Activity，在倒计时结束后自动弹出显示
 * <p>
 * Author:huangwubin
 * Contacts:<EMAIL>
 * <p>
 * changeLogs:
 * 2025/7/8: First created this class.
 */
class AppDataCleanupActivity : AppCompatActivity() {

    // ViewModel
    private val cleanupViewModel by viewModels<AppDataCleanupViewModel>()

    // UI组件
    private lateinit var tvStatusText: android.widget.TextView
    private lateinit var layoutProgressContainer: android.widget.LinearLayout
    private lateinit var rvCleanupProgress: RecyclerView
    private lateinit var layoutCompletionHint: android.widget.LinearLayout

    // 适配器
    private lateinit var progressAdapter: AppCleanupProgressAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        XLog.d("AppDataCleanupActivity启动")
        
        setContentView(R.layout.activity_app_data_cleanup)

        // 设置全屏和半透明背景
        setupFullscreenAndTransparent()
        
        // 隐藏ActionBar
        supportActionBar?.hide()

        // 初始化UI组件
        initViews()
        
        // 设置观察者
        setupObservers()
        
        // 启动清理流程
        cleanupViewModel.startCleanupProcess()
    }

    /**
     * 设置全屏和半透明背景
     */
    private fun setupFullscreenAndTransparent() {
        try {
            // 设置全屏标志
            window.setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN
            )
            
            // 设置保持屏幕常亮
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            
            // 隐藏系统UI
            window.decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_FULLSCREEN or
                View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            )
            
        } catch (e: Exception) {
            XLog.e("设置全屏模式失败", e)
        }
    }

    /**
     * 初始化UI组件
     */
    private fun initViews() {
        try {
            tvStatusText = findViewById(R.id.tv_status_text)
            layoutProgressContainer = findViewById(R.id.layout_progress_container)
            rvCleanupProgress = findViewById(R.id.rv_cleanup_progress)
            layoutCompletionHint = findViewById(R.id.layout_completion_hint)

            // 设置RecyclerView
            progressAdapter = AppCleanupProgressAdapter()
            rvCleanupProgress.apply {
                layoutManager = LinearLayoutManager(this@AppDataCleanupActivity)
                adapter = progressAdapter
                setHasFixedSize(true)
            }

        } catch (e: Exception) {
            XLog.e("初始化UI组件失败", e)
        }
    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        try {
            // 观察清理状态
            cleanupViewModel.cleanupState.observe(this) { state ->
                handleCleanupStateChange(state)
            }

            // 观察状态文本
            cleanupViewModel.statusText.observe(this) { text ->
                tvStatusText.text = text
            }

            // 观察进度列表
            cleanupViewModel.progressItems.observe(this) { items ->
                progressAdapter.submitList(items)
                
                // 如果有进度项，显示进度容器
                if (items.isNotEmpty()) {
                    layoutProgressContainer.visibility = View.VISIBLE
                }
            }

            // 观察清理完成
            cleanupViewModel.cleanupCompleted.observe(this) { completed ->
                if (completed) {
                    handleCleanupCompleted()
                }
            }

        } catch (e: Exception) {
            XLog.e("设置观察者失败", e)
        }
    }

    /**
     * 处理清理状态变化
     */
    private fun handleCleanupStateChange(state: AppDataCleanupViewModel.CleanupState) {
        try {
            when (state) {
                AppDataCleanupViewModel.CleanupState.PREPARING -> {
                    // 准备阶段，显示Loading
                }
                
                AppDataCleanupViewModel.CleanupState.BUSINESS_CHECK -> {
                    // 业务判断阶段
                }
                
                AppDataCleanupViewModel.CleanupState.CLEANING -> {
                    // 清理阶段，显示完成提示
                    layoutCompletionHint.visibility = View.VISIBLE
                }
                
                AppDataCleanupViewModel.CleanupState.COMPLETED -> {
                    // 清理完成
                    XLog.d("应用数据清理流程完成")
                }
                
                AppDataCleanupViewModel.CleanupState.ERROR -> {
                    // 清理出错
                    XLog.e("应用数据清理流程出错")
                }
            }
        } catch (e: Exception) {
            XLog.e("处理清理状态变化失败", e)
        }
    }

    /**
     * 处理清理完成
     */
    private fun handleCleanupCompleted() {
        try {
            XLog.d("应用数据清理完成，准备重启应用")
            
            // 延迟2秒后重启应用，让用户看到完成状态
            lifecycleScope.launch {
                delay(1000)
                
                // 重启应用
                AdminManager.restartApp()
                
                // 结束当前Activity
                finish()
            }
            
        } catch (e: Exception) {
            XLog.e("处理清理完成失败", e)
        }
    }

    /**
     * 禁用返回键
     */
    override fun onBackPressed() {
        // 清理过程中禁用返回键
        XLog.d("清理过程中禁用返回键")
    }

    override fun onDestroy() {
        try {
            XLog.d("AppDataCleanupActivity销毁")
            super.onDestroy()
        } catch (e: Exception) {
            XLog.e("AppDataCleanupActivity销毁失败", e)
        }
    }
}
